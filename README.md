# GetPocket to Org-Roam Converter

This Python script converts GetPocket CSV exports into individual org-roam files with annotations and metadata.

## Features

- ✅ Converts each GetPocket entry into a separate org-roam file
- ✅ Preserves all metadata (title, URL, tags, status, date added)
- ✅ Includes annotations and highlights from the JSON file
- ✅ Creates proper org-roam properties and tags
- ✅ Sanitizes filenames for filesystem compatibility
- ✅ Uses timestamp_title naming convention
- ✅ Processes all entries (unread, archive, etc.)
- ✅ **Downloads and caches web pages locally for offline access**
- ✅ **Creates clickable links to cached copies in org-roam files**
- ✅ **Includes Internet Archive (Wayback Machine) links as backup**
- ✅ **Automatically checks for archived snapshots when available**
- ✅ **Parallel processing for faster conversion**
- ✅ **Rich terminal interface with live dashboard (optional)**
- ✅ **Enhanced content processing with readability extraction**
- ✅ **Advanced metadata extraction (author, description, word count)**
- ✅ **Intelligent HTML cleaning and asset handling**
- ✅ **Better encoding detection and error handling**

## Usage

1. **Extract your GetPocket export:**
   ```bash
   unzip pocket.zip
   ```

2. **Install dependencies (for enhanced features):**
   ```bash
   pip install -r requirements.txt
   # Or run the setup script
   python3 setup_enhanced.py
   ```

3. **Run the converter:**

   **Option A: Enhanced converter with advanced content processing (recommended)**
   ```bash
   python3 pocket_to_org_roam_enhanced.py
   ```

   **Option B: Parallel converter with rich interface**
   ```bash
   python3 run_parallel_converter.py
   ```

   **Option C: Basic converter (sequential processing)**
   ```bash
   python3 pocket_to_org_roam.py
   ```

   **Option D: Custom settings**
   ```bash
   python3 pocket_to_org_roam_enhanced.py --workers 8 --max-entries 100
   ```

4. **Find your org-roam files in the output directory**
   - Org-roam files: `export_enhanced/*.org` (or `export/` for other converters)
   - Cached web pages: `export_enhanced/local_cache/*.html`
   - Downloaded assets: `export_enhanced/local_cache/assets/`

## File Structure

Each generated org-roam file includes:

- **Title and metadata** in org-mode format
- **Org-roam properties** (`:ROAM_REFS:`, `:ROAM_TAGS:`, etc.)
- **Tags** in both org-mode and org-roam format
- **URL link** to the original article
- **Local cache link** to downloaded copy (if available)
- **Internet Archive link** to Wayback Machine snapshot (if available)
- **Highlights and annotations** (if available)
- **Notes section** for your own notes

## Example Output

```org
#+TITLE: How To Look Like A UNIX Guru
#+DATE: 2017-09-27 05:46:27
:PROPERTIES:
:ROAM_REFS: http://www2.cs.usfca.edu/~parrt/course/601/lectures/unix.util.html
:ROAM_TAGS: starred
:POCKET_STATUS: archive
:POCKET_TIME_ADDED: 1506483987
:END:

#+TAGS: :starred:

* How To Look Like A UNIX Guru :starred:

- URL: [[http://www2.cs.usfca.edu/~parrt/course/601/lectures/unix.util.html][http://www2.cs.usfca.edu/~parrt/course/601/lectures/unix.util.html]]
- Local Cache: [[file:local_cache/cs.usfca.edu_parrt_course_601_lectures_unix.util.html][Cached Copy]]
- Internet Archive: [[http://web.archive.org/web/20201125142301/http://www2.cs.usfca.edu/~parrt/course/601/lectures/unix.util.html][Wayback Machine]]
- Status: archive
- Date Added: 2017-09-27 05:46:27
- Tags: starred

** Highlights and Annotations

#+BEGIN_QUOTE
Volunteer to teach whatever you can whenever you can
#+END_QUOTE
/Highlighted on: 2021-06-04 16:35:46/

** Notes

<!-- Add your notes here -->
```

## Files Processed

- **CSV file:** `part_000000.csv` (6,607 entries processed)
- **Annotations:** `annotations/part_000000.json` (1 URL with highlights)
- **Output:** `export/` directory with individual `.org` files
- **Local cache:** `export/local_cache/` directory with downloaded HTML files

## Filename Convention

Files are named using the format: `YYYYMMDD_HHMMSS_sanitized-title.org`

Example: `20170927_054627_how-to-look-like-a-unix-guru.org`

## Requirements

### Basic Converter
- Python 3.6+
- Standard library modules only (csv, json, os, re, datetime, pathlib, typing)

### Enhanced Converter (Recommended)
- Python 3.6+
- beautifulsoup4>=4.12.0 (HTML parsing and cleaning)
- lxml>=4.9.0 (Fast XML/HTML parser)
- readability-lxml>=0.8.1 (Article content extraction)
- requests>=2.31.0 (Enhanced HTTP handling)
- chardet>=5.0.0 (Encoding detection)
- rich>=13.0.0 (Terminal interface)

## Customization

You can modify the script to:
- Change the output directory
- Adjust the filename format
- Modify the org-mode template
- Filter entries by status or tags
- Add additional metadata fields
- Disable URL caching by setting `download_cache=False`
- Adjust download timeout and retry settings

## Integration with Org-Roam

To use these files with org-roam:

1. Move the `export/` directory contents to your org-roam directory
2. Run `org-roam-db-sync` in Emacs to index the new files
3. Use `org-roam-node-find` to search and navigate your GetPocket articles

The `:ROAM_REFS:` property allows org-roam to automatically link to these files when you reference the URLs elsewhere in your notes.

## Local Caching & Internet Archive Integration

The script provides multiple layers of content preservation:

### Benefits:
- **Offline access** to your saved articles via local cache
- **Content preservation** even if original sites go down
- **Faster loading** when reviewing articles locally
- **Internet Archive backup** for additional preservation
- **Historical snapshots** via Wayback Machine links
- **No dependency** on internet connection for reading cached content

### How it works:
1. **Local caching**: For each URL, the script downloads the webpage
2. **Files saved** in `export/local_cache/` with sanitized filenames
3. **Internet Archive check**: Queries Wayback Machine API for archived snapshots
4. **Multiple links**: Each org-roam file includes links to:
   - Original URL
   - Local cached copy (if download successful)
   - Internet Archive snapshot (if available)
5. **HTML format**: Cached files can be opened in any browser

### Filename format:
- `domain.com_path_to_page.html`
- Example: `paulgraham.com_essays_startup.html`

### Error handling:
- **Failed downloads** are logged but don't stop processing
- **Retries** with exponential backoff for temporary failures
- **Rate limiting** respect (HTTP 429 errors)
- **Wayback Machine fallback** when direct download fails
- **Generic archive links** provided even when no specific snapshot found

### Performance considerations:
- **Sequential processing** to avoid overwhelming servers
- **30-second timeout** for each download
- **10-second timeout** for Wayback Machine API calls
- **Skip cached files** on subsequent runs
- **Parallel archive checking** for efficiency

### Internet Archive Integration:
- **API-based checking** for available snapshots
- **Timestamped URLs** when specific snapshots exist
- **Generic archive URLs** as fallback
- **Respectful querying** with appropriate delays

For testing purposes, use `test_enhanced_converter.py` which processes only the first 10 entries with enhanced features.

## Quick Start Guide

1. **Setup**:
   ```bash
   git clone <repository>
   cd getpocket_to_org
   python3 setup_enhanced.py  # Check dependencies
   pip install -r requirements.txt
   ```

2. **Extract GetPocket data**:
   ```bash
   unzip pocket.zip  # Your GetPocket export
   ```

3. **Run enhanced converter**:
   ```bash
   python3 pocket_to_org_roam_enhanced.py
   ```

4. **Test with sample**:
   ```bash
   python3 test_enhanced_converter.py
   ```

## File Overview

| File | Purpose |
|------|---------|
| `pocket_to_org_roam.py` | Basic converter (no dependencies) |
| `pocket_to_org_roam_parallel.py` | Parallel processing with rich UI |
| `pocket_to_org_roam_enhanced.py` | **Enhanced with content processing** |
| `content_processor.py` | Enhanced content processing engine |
| `test_enhanced_converter.py` | Test enhanced features |
| `setup_enhanced.py` | Dependency checker and installer |
| `ENHANCED_FEATURES.md` | Detailed enhanced features documentation |

## Documentation

- **README.md** - This file (general usage)
- **ENHANCED_FEATURES.md** - Detailed enhanced features documentation
- **requirements.txt** - Python dependencies

## Enhanced Content Processing

The enhanced converter (`pocket_to_org_roam_enhanced.py`) provides significant improvements:

### 🧹 **Advanced HTML Cleaning**
- Removes ads, tracking scripts, and unnecessary elements
- Cleans up HTML attributes to reduce file size
- Preserves essential content while removing clutter

### 📊 **Rich Metadata Extraction**
- **Title**: Extracts from multiple sources (title tag, Open Graph, Twitter)
- **Author**: From meta tags and structured data
- **Description**: Meta descriptions and Open Graph data
- **Word Count & Reading Time**: Calculated from article content
- **Keywords**: Converted to org-roam tags

### 📖 **Readability Processing**
- Extracts main article content using readability algorithms
- Provides clean article summaries in org-roam files
- Focuses on readable content, removing navigation and sidebars

### 🎨 **Enhanced Asset Handling**
- Smarter detection of CSS, JavaScript, and images
- Better error handling and retry logic
- Content-type aware downloading
- Tracks download statistics and file sizes

### Example Enhanced Output

```org
** Content Information

**Title:** How to Build Better Software
**Author:** John Developer
**Site:** TechBlog
**Description:** A comprehensive guide to software development
**Word Count:** 2,847
**Reading Time:** 14 minutes
**Language:** en

** Article Summary

#+BEGIN_QUOTE
This article explores fundamental principles of building robust,
maintainable software systems...
#+END_QUOTE
```

## Parallel Processing & Rich Interface

The enhanced parallel version provides significant performance improvements and a beautiful terminal interface:

### Features:
- **Multi-threaded processing** for simultaneous downloads and conversions
- **Real-time dashboard** with live statistics and progress bars
- **Configurable worker threads** (default: 8-10 workers)
- **Live performance metrics** (entries/second, success rates, etc.)
- **Graceful interruption** with Ctrl+C
- **Automatic rich library installation**

### Performance Benefits:
- **5-10x faster** processing compared to sequential version
- **Concurrent downloads** and Wayback Machine checks
- **Efficient resource utilization** with thread pooling
- **Real-time feedback** on processing status

### Rich Interface Features:
- **Live dashboard** with color-coded statistics
- **Progress tracking** with percentage completion
- **Success/failure rates** for downloads and archive checks
- **Processing speed** in entries per second
- **Elapsed time** and estimated completion
- **Beautiful tables** and progress bars

### Command Line Options:
```bash
python3 pocket_to_org_roam_parallel.py --help

Options:
  --csv CSV_FILE              CSV file path (default: part_000000.csv)
  --annotations ANNOTATIONS   Annotations file path (default: annotations/part_000000.json)
  --output OUTPUT_DIR         Output directory (default: export_parallel)
  --workers NUM_WORKERS       Number of parallel workers (default: 10)
  --max-entries MAX_ENTRIES   Maximum entries to process (for testing)
```

### Example Usage:
```bash
# Process all entries with 12 workers
python3 pocket_to_org_roam_parallel.py --workers 12

# Test with first 50 entries
python3 pocket_to_org_roam_parallel.py --max-entries 50 --workers 5

# Custom output directory
python3 pocket_to_org_roam_parallel.py --output my_org_files --workers 8
```
