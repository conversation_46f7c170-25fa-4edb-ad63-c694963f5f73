#!/usr/bin/env python3
"""
Enhanced Content Processor for GetPocket to Org-Roam Converter

Provides advanced HTML processing, content cleaning, readability extraction,
and metadata extraction capabilities.
"""

import os
import re
import time
import hashlib
import mimetypes
from typing import Dict, List, Optional, Tuple, Set, NamedTuple
from urllib.parse import urljoin, urlparse, unquote
from pathlib import Path

# Optional imports with fallbacks
try:
    import requests
    from requests.adapters import HTTPAdapter
    from requests.packages.urllib3.util.retry import Retry
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    import urllib.request
    import urllib.error

try:
    from bs4 import BeautifulSoup, Comment
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    from readability import Document
    READABILITY_AVAILABLE = True
except ImportError:
    READABILITY_AVAILABLE = False

try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False


class ContentMetadata(NamedTuple):
    """Structured metadata extracted from web content."""
    title: str = ""
    description: str = ""
    author: str = ""
    published_date: str = ""
    site_name: str = ""
    article_text: str = ""
    word_count: int = 0
    reading_time: int = 0  # minutes
    language: str = ""
    tags: List[str] = []


class AssetInfo(NamedTuple):
    """Information about a downloaded asset."""
    original_url: str
    local_path: str
    content_type: str
    size: int
    success: bool


class EnhancedContentProcessor:
    """Enhanced content processor with readability extraction and better asset handling."""
    
    def __init__(self, cache_dir: str, max_workers: int = 5):
        self.cache_dir = Path(cache_dir)
        self.assets_dir = self.cache_dir / "assets"
        self.assets_dir.mkdir(parents=True, exist_ok=True)
        
        self.downloaded_assets: Set[str] = set()
        self.asset_map: Dict[str, str] = {}
        self.failed_assets: Set[str] = set()
        
        # Setup HTTP session with retries if requests is available
        if REQUESTS_AVAILABLE:
            self.session = requests.Session()
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            self.session.mount("http://", adapter)
            self.session.mount("https://", adapter)
            
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
        else:
            self.session = None
    
    def detect_encoding(self, content: bytes) -> str:
        """Detect content encoding."""
        if CHARDET_AVAILABLE:
            result = chardet.detect(content)
            return result.get('encoding', 'utf-8') or 'utf-8'
        return 'utf-8'
    
    def download_content(self, url: str, timeout: int = 30) -> Tuple[bool, bytes, str]:
        """Download content with proper error handling and encoding detection."""
        try:
            if REQUESTS_AVAILABLE and self.session:
                response = self.session.get(url, timeout=timeout, stream=True)
                response.raise_for_status()
                
                content = response.content
                content_type = response.headers.get('content-type', '')
                
                return True, content, content_type
            else:
                # Fallback to urllib
                req = urllib.request.Request(url, headers={
                    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
                })
                
                with urllib.request.urlopen(req, timeout=timeout) as response:
                    content = response.read()
                    content_type = response.headers.get('content-type', '')
                    
                return True, content, content_type
                
        except Exception as e:
            return False, b'', ''
    
    def extract_metadata(self, html_content: str, url: str) -> ContentMetadata:
        """Extract comprehensive metadata from HTML content."""
        if not BS4_AVAILABLE:
            return ContentMetadata()
        
        soup = BeautifulSoup(html_content, 'lxml')
        
        # Extract title
        title = ""
        if soup.title:
            title = soup.title.get_text().strip()
        
        # Try Open Graph title
        if not title:
            og_title = soup.find('meta', property='og:title')
            if og_title:
                title = og_title.get('content', '').strip()
        
        # Try Twitter title
        if not title:
            twitter_title = soup.find('meta', attrs={'name': 'twitter:title'})
            if twitter_title:
                title = twitter_title.get('content', '').strip()
        
        # Extract description
        description = ""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            description = meta_desc.get('content', '').strip()
        
        if not description:
            og_desc = soup.find('meta', property='og:description')
            if og_desc:
                description = og_desc.get('content', '').strip()
        
        # Extract author
        author = ""
        author_meta = soup.find('meta', attrs={'name': 'author'})
        if author_meta:
            author = author_meta.get('content', '').strip()
        
        if not author:
            # Try structured data
            author_elem = soup.find('span', class_=re.compile(r'author', re.I))
            if author_elem:
                author = author_elem.get_text().strip()
        
        # Extract site name
        site_name = ""
        og_site = soup.find('meta', property='og:site_name')
        if og_site:
            site_name = og_site.get('content', '').strip()
        
        # Extract article text using readability if available
        article_text = ""
        word_count = 0
        reading_time = 0
        
        if READABILITY_AVAILABLE:
            try:
                doc = Document(html_content)
                article_text = doc.summary()
                
                # Clean up the article text
                if BS4_AVAILABLE:
                    article_soup = BeautifulSoup(article_text, 'lxml')
                    article_text = article_soup.get_text()
                    
                # Calculate word count and reading time
                words = len(article_text.split())
                word_count = words
                reading_time = max(1, words // 200)  # Assume 200 words per minute
                
            except Exception:
                pass
        
        # Extract language
        language = ""
        html_tag = soup.find('html')
        if html_tag:
            language = html_tag.get('lang', '').strip()
        
        # Extract keywords as tags
        tags = []
        keywords_meta = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_meta:
            keywords = keywords_meta.get('content', '')
            tags = [tag.strip() for tag in keywords.split(',') if tag.strip()]
        
        return ContentMetadata(
            title=title,
            description=description,
            author=author,
            site_name=site_name,
            article_text=article_text,
            word_count=word_count,
            reading_time=reading_time,
            language=language,
            tags=tags
        )
    
    def clean_html_content(self, html_content: str) -> str:
        """Clean HTML content for better readability and caching."""
        if not BS4_AVAILABLE:
            return html_content
        
        soup = BeautifulSoup(html_content, 'lxml')
        
        # Remove unwanted elements
        unwanted_tags = [
            'script', 'style', 'noscript', 'iframe', 'embed', 'object',
            'applet', 'form', 'input', 'button', 'select', 'textarea'
        ]
        
        for tag_name in unwanted_tags:
            for tag in soup.find_all(tag_name):
                tag.decompose()
        
        # Remove comments
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # Remove elements with common ad/tracking classes
        ad_classes = [
            'advertisement', 'ads', 'ad-', 'google-ad', 'adsystem',
            'tracking', 'analytics', 'social-share', 'popup', 'modal',
            'newsletter', 'subscription', 'cookie-notice'
        ]
        
        for class_pattern in ad_classes:
            for elem in soup.find_all(class_=re.compile(class_pattern, re.I)):
                elem.decompose()
        
        # Clean up attributes to reduce file size
        for tag in soup.find_all():
            # Keep only essential attributes
            essential_attrs = ['href', 'src', 'alt', 'title', 'class', 'id']
            attrs_to_remove = []
            
            for attr in tag.attrs:
                if attr not in essential_attrs:
                    attrs_to_remove.append(attr)
            
            for attr in attrs_to_remove:
                del tag.attrs[attr]
        
        return str(soup)
    
    def get_safe_filename(self, url: str, content_type: str = "") -> str:
        """Generate a safe filename for cached content."""
        parsed = urlparse(url)
        
        # Create URL hash for uniqueness
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        
        # Determine file extension
        ext = ""
        if content_type:
            ext = mimetypes.guess_extension(content_type.split(';')[0]) or ""
        
        if not ext and parsed.path:
            path_ext = os.path.splitext(parsed.path)[1]
            if path_ext:
                ext = path_ext[:10]  # Limit extension length
        
        # Create safe filename
        domain = parsed.netloc.replace('www.', '')
        safe_domain = re.sub(r'[^\w\-_.]', '_', domain)
        
        # Add path info if available
        path_part = ""
        if parsed.path and parsed.path != '/':
            path_clean = re.sub(r'[^\w\-_.]', '_', parsed.path.strip('/'))
            path_part = f"_{path_clean[:50]}"  # Limit path length
        
        filename = f"{safe_domain}{path_part}_{url_hash}{ext}"
        
        # Ensure filename isn't too long
        if len(filename) > 200:
            filename = f"{safe_domain}_{url_hash}{ext}"
        
        return filename

    def download_asset(self, url: str, base_url: str) -> AssetInfo:
        """Download an asset with enhanced error handling."""
        if not url or url in self.failed_assets:
            return AssetInfo(url, "", "", 0, False)

        if url in self.downloaded_assets:
            local_path = self.asset_map.get(url, "")
            return AssetInfo(url, local_path, "", 0, True)

        try:
            # Resolve relative URLs
            absolute_url = urljoin(base_url, url)

            # Skip problematic URLs
            if absolute_url.startswith(('data:', 'javascript:', 'mailto:', 'tel:')):
                return AssetInfo(url, "", "", 0, False)

            # Check if already downloaded
            if absolute_url in self.downloaded_assets:
                local_path = self.asset_map.get(absolute_url, "")
                return AssetInfo(url, local_path, "", 0, True)

            # Download the asset
            success, content, content_type = self.download_content(absolute_url, timeout=15)

            if not success:
                self.failed_assets.add(url)
                self.failed_assets.add(absolute_url)
                return AssetInfo(url, "", "", 0, False)

            # Generate filename and save
            filename = self.get_safe_filename(absolute_url, content_type)
            filepath = self.assets_dir / filename

            with open(filepath, 'wb') as f:
                f.write(content)

            # Update mappings
            local_path = f"assets/{filename}"
            self.asset_map[url] = local_path
            self.asset_map[absolute_url] = local_path
            self.downloaded_assets.add(absolute_url)

            return AssetInfo(url, local_path, content_type, len(content), True)

        except Exception:
            self.failed_assets.add(url)
            return AssetInfo(url, "", "", 0, False)

    def rewrite_html_urls(self, html_content: str, base_url: str) -> Tuple[str, List[AssetInfo]]:
        """Rewrite URLs in HTML content with enhanced asset handling."""
        if not BS4_AVAILABLE:
            return html_content, []

        soup = BeautifulSoup(html_content, 'lxml')
        asset_info = []

        # Define URL patterns to process
        url_patterns = [
            ('link', 'href', ['stylesheet', 'icon', 'shortcut icon']),
            ('script', 'src', []),
            ('img', 'src', []),
            ('source', 'src', []),
            ('video', 'src', []),
            ('audio', 'src', []),
        ]

        for tag_name, attr_name, rel_filter in url_patterns:
            for tag in soup.find_all(tag_name):
                # Apply rel filter for link tags
                if rel_filter and tag_name == 'link':
                    rel = tag.get('rel', [])
                    if isinstance(rel, str):
                        rel = [rel]
                    if not any(r in rel_filter for r in rel):
                        continue

                url = tag.get(attr_name)
                if not url:
                    continue

                # Download and replace URL
                asset = self.download_asset(url, base_url)
                asset_info.append(asset)

                if asset.success and asset.local_path:
                    tag[attr_name] = asset.local_path

        # Handle CSS url() references
        for style_tag in soup.find_all('style'):
            if style_tag.string:
                css_content = style_tag.string
                css_content = self._rewrite_css_urls(css_content, base_url, asset_info)
                style_tag.string = css_content

        # Handle inline styles
        for tag in soup.find_all(style=True):
            style_content = tag['style']
            style_content = self._rewrite_css_urls(style_content, base_url, asset_info)
            tag['style'] = style_content

        return str(soup), asset_info

    def _rewrite_css_urls(self, css_content: str, base_url: str, asset_info: List[AssetInfo]) -> str:
        """Rewrite URLs in CSS content."""
        # Pattern to match url() in CSS
        url_pattern = re.compile(r'url\s*\(\s*["\']?([^"\')\s]+)["\']?\s*\)', re.IGNORECASE)

        def replace_url(match):
            url = match.group(1)
            asset = self.download_asset(url, base_url)
            asset_info.append(asset)

            if asset.success and asset.local_path:
                return f'url("{asset.local_path}")'
            return match.group(0)

        return url_pattern.sub(replace_url, css_content)

    def create_enhanced_cached_copy(self, url: str) -> Tuple[bool, str, ContentMetadata, List[AssetInfo]]:
        """Create an enhanced cached copy with content processing and metadata extraction."""
        if not url or not url.startswith(('http://', 'https://')):
            return False, "", ContentMetadata(), []

        try:
            # Generate filename for the main HTML file
            filename = self.get_safe_filename(url, 'text/html')
            if not filename.endswith('.html'):
                filename += '.html'

            filepath = self.cache_dir / filename

            # Skip if already exists
            if filepath.exists():
                return True, filename, ContentMetadata(), []

            # Download the main HTML content
            success, content, _ = self.download_content(url, timeout=30)

            if not success:
                return False, "", ContentMetadata(), []

            # Detect encoding and decode
            encoding = self.detect_encoding(content)
            try:
                html_content = content.decode(encoding, errors='ignore')
            except (UnicodeDecodeError, LookupError):
                html_content = content.decode('utf-8', errors='ignore')

            # Extract metadata before cleaning
            metadata = self.extract_metadata(html_content, url)

            # Clean HTML content
            cleaned_html = self.clean_html_content(html_content)

            # Rewrite URLs and download assets
            final_html, asset_info = self.rewrite_html_urls(cleaned_html, url)

            # Add metadata as HTML comments for reference
            metadata_comment = f"""
<!-- Cached by GetPocket to Org-Roam Converter -->
<!-- Original URL: {url} -->
<!-- Title: {metadata.title} -->
<!-- Author: {metadata.author} -->
<!-- Word Count: {metadata.word_count} -->
<!-- Reading Time: {metadata.reading_time} minutes -->
<!-- Cached on: {time.strftime('%Y-%m-%d %H:%M:%S')} -->
"""

            # Insert metadata comment after <head> tag
            if '<head>' in final_html:
                final_html = final_html.replace('<head>', f'<head>{metadata_comment}', 1)
            else:
                final_html = metadata_comment + final_html

            # Save the enhanced HTML
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(final_html)

            return True, filename, metadata, asset_info

        except Exception as e:
            return False, "", ContentMetadata(), []

    def get_cache_stats(self) -> Dict[str, int]:
        """Get statistics about cached content."""
        stats = {
            'total_files': 0,
            'total_size': 0,
            'html_files': 0,
            'asset_files': 0,
            'failed_downloads': len(self.failed_assets)
        }

        try:
            # Count main cache files
            for item in self.cache_dir.iterdir():
                if item.is_file():
                    stats['total_files'] += 1
                    stats['total_size'] += item.stat().st_size
                    if item.suffix == '.html':
                        stats['html_files'] += 1

            # Count asset files
            if self.assets_dir.exists():
                for item in self.assets_dir.iterdir():
                    if item.is_file():
                        stats['total_files'] += 1
                        stats['asset_files'] += 1
                        stats['total_size'] += item.stat().st_size

        except Exception:
            pass

        return stats

    def cleanup_failed_assets(self):
        """Clean up tracking of failed assets (for retry purposes)."""
        self.failed_assets.clear()


def create_readable_summary(metadata: ContentMetadata) -> str:
    """Create a readable summary from extracted metadata."""
    summary_parts = []

    if metadata.title:
        summary_parts.append(f"**Title:** {metadata.title}")

    if metadata.author:
        summary_parts.append(f"**Author:** {metadata.author}")

    if metadata.site_name:
        summary_parts.append(f"**Site:** {metadata.site_name}")

    if metadata.description:
        summary_parts.append(f"**Description:** {metadata.description}")

    if metadata.word_count > 0:
        summary_parts.append(f"**Word Count:** {metadata.word_count:,}")

    if metadata.reading_time > 0:
        summary_parts.append(f"**Reading Time:** {metadata.reading_time} minutes")

    if metadata.language:
        summary_parts.append(f"**Language:** {metadata.language}")

    if metadata.tags:
        tags_str = ", ".join(metadata.tags[:10])  # Limit to first 10 tags
        summary_parts.append(f"**Keywords:** {tags_str}")

    return "\n".join(summary_parts)


def format_asset_summary(assets: List[AssetInfo]) -> str:
    """Format a summary of downloaded assets."""
    if not assets:
        return "No assets downloaded."

    successful = [a for a in assets if a.success]
    failed = [a for a in assets if not a.success]

    total_size = sum(a.size for a in successful)

    summary = f"Downloaded {len(successful)} assets"
    if total_size > 0:
        if total_size > 1024 * 1024:
            summary += f" ({total_size / (1024 * 1024):.1f} MB)"
        elif total_size > 1024:
            summary += f" ({total_size / 1024:.1f} KB)"
        else:
            summary += f" ({total_size} bytes)"

    if failed:
        summary += f", {len(failed)} failed"

    return summary
