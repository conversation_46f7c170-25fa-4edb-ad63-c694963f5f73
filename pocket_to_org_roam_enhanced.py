#!/usr/bin/env python3
"""
GetPocket to Org-Roam Converter - Enhanced Version

Enhanced version with improved content processing, readability extraction,
and better asset handling using the new content processor.
"""

import csv
import json
import os
import re
import time
import urllib.parse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import threading

# Import our enhanced content processor
from content_processor import EnhancedContentProcessor, ContentMetadata, AssetInfo, create_readable_summary, format_asset_summary

# Rich imports for beautiful terminal interface
try:
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn
    from rich.table import Table
    from rich.layout import Layout
    from rich.panel import Panel
    from rich.live import Live
    from rich.text import Text
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Rich library not available. Install with: pip install rich")
    print("Falling back to basic output...")

    # Create dummy classes when rich is not available
    class Table:
        def __init__(self, *args, **kwargs): pass
        def add_column(self, *args, **kwargs): pass
        def add_row(self, *args, **kwargs): pass

    class Console:
        def print(self, *args, **kwargs): print(*args)

    class Layout:
        def __init__(self, *args, **kwargs): pass
        def split_column(self, *args, **kwargs): pass
        def __getitem__(self, key): return self
        def update(self, *args, **kwargs): pass

    class Panel:
        def __init__(self, *args, **kwargs): pass

    class Text:
        def __init__(self, *args, **kwargs): pass

    class Live:
        def __init__(self, *args, **kwargs): pass
        def __enter__(self): return self
        def __exit__(self, *args): pass


@dataclass
class EnhancedProcessingStats:
    """Enhanced statistics for processing progress."""
    total_entries: int = 0
    processed_entries: int = 0
    error_entries: int = 0
    download_success: int = 0
    download_fail: int = 0
    wayback_success: int = 0
    wayback_fail: int = 0
    assets_downloaded: int = 0
    assets_failed: int = 0
    total_content_size: int = 0
    metadata_extracted: int = 0
    readability_extracted: int = 0
    start_time: float = 0
    
    def __post_init__(self):
        if self.start_time == 0:
            self.start_time = time.time()
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time
    
    @property
    def entries_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.processed_entries / self.elapsed_time
        return 0


class EnhancedPocketConverter:
    """Enhanced converter with improved content processing."""

    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.stats = EnhancedProcessingStats()
        self.stats_lock = threading.Lock()
        self.console = Console() if RICH_AVAILABLE else None
        
    def sanitize_filename(self, text: str, max_length: int = 100) -> str:
        """Sanitize text for use as filename."""
        sanitized = re.sub(r'[<>:"/\\|?*]', '', text)
        sanitized = re.sub(r'[^\w\s-]', '', sanitized)
        sanitized = re.sub(r'[-\s]+', '-', sanitized)
        sanitized = sanitized.strip('-').lower()
        
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length].rstrip('-')
        
        return sanitized

    def timestamp_to_date(self, timestamp: str) -> str:
        """Convert Unix timestamp to readable date."""
        try:
            dt = datetime.fromtimestamp(int(timestamp))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, TypeError):
            return timestamp

    def get_wayback_machine_url(self, url: str, timestamp: str = "") -> str:
        """Generate a Wayback Machine URL for the given URL."""
        if not url:
            return ""
        
        clean_url = url.strip()
        
        if timestamp:
            return f"https://web.archive.org/web/{timestamp}/{clean_url}"
        else:
            return f"https://web.archive.org/web/{clean_url}"

    def check_wayback_machine_availability(self, url: str) -> Tuple[bool, str]:
        """Check if a URL is available in the Wayback Machine."""
        if not url:
            return False, ""
        
        try:
            import urllib.request
            api_url = f"https://archive.org/wayback/available?url={urllib.parse.quote(url)}"
            
            req = urllib.request.Request(
                api_url,
                headers={
                    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
            )
            
            with urllib.request.urlopen(req, timeout=10) as response:
                data = json.loads(response.read().decode('utf-8'))
                
                if data.get('archived_snapshots', {}).get('closest', {}).get('available'):
                    wayback_url = data['archived_snapshots']['closest']['url']
                    return True, wayback_url
                else:
                    return False, self.get_wayback_machine_url(url)
                    
        except Exception:
            return False, self.get_wayback_machine_url(url)

    def load_annotations(self, annotations_file: str) -> Dict[str, List[Dict]]:
        """Load annotations from JSON file."""
        annotations = {}
        
        if not os.path.exists(annotations_file):
            return annotations
        
        try:
            with open(annotations_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            for item in data:
                url = item.get('url', '')
                highlights = item.get('highlights', [])
                if url and highlights:
                    annotations[url] = highlights
                    
        except (json.JSONDecodeError, FileNotFoundError):
            pass
            
        return annotations

    def create_enhanced_org_content(self, entry: Dict, annotations: Dict[str, List[Dict]], 
                                   local_filename: str = "", wayback_url: str = "",
                                   metadata: Optional[ContentMetadata] = None,
                                   assets: Optional[List[AssetInfo]] = None) -> str:
        """Create enhanced org-mode content with metadata and asset information."""
        title = entry.get('title', 'Untitled').strip('"')
        url = entry.get('url', '')
        time_added = entry.get('time_added', '')
        tags = entry.get('tags', '')
        status = entry.get('status', '')
        
        # Use extracted title if available and better
        if metadata and metadata.title and len(metadata.title) > len(title):
            title = metadata.title
        
        date_added = self.timestamp_to_date(time_added)
        
        # Process tags
        org_tags = ''
        roam_tags = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split('|') if tag.strip()]
            if tag_list:
                org_tags = ':' + ':'.join(tag_list) + ':'
                roam_tags = tag_list
        
        # Add extracted keywords as tags
        if metadata and metadata.tags:
            extracted_tags = [tag.lower().replace(' ', '_') for tag in metadata.tags[:5]]  # Limit to 5
            roam_tags.extend(extracted_tags)
            if org_tags:
                org_tags = org_tags[:-1] + ':' + ':'.join(extracted_tags) + ':'
            else:
                org_tags = ':' + ':'.join(extracted_tags) + ':'
        
        # Start building content
        content = f"#+TITLE: {title}\n"
        content += f"#+DATE: {date_added}\n"
        
        # Add org-roam properties
        content += ":PROPERTIES:\n"
        content += f":ROAM_REFS: {url}\n"
        if roam_tags:
            content += f":ROAM_TAGS: {' '.join(roam_tags)}\n"
        content += f":POCKET_STATUS: {status}\n"
        content += f":POCKET_TIME_ADDED: {time_added}\n"
        
        # Add enhanced metadata properties
        if metadata:
            if metadata.author:
                content += f":AUTHOR: {metadata.author}\n"
            if metadata.site_name:
                content += f":SITE_NAME: {metadata.site_name}\n"
            if metadata.word_count > 0:
                content += f":WORD_COUNT: {metadata.word_count}\n"
            if metadata.reading_time > 0:
                content += f":READING_TIME: {metadata.reading_time}\n"
            if metadata.language:
                content += f":LANGUAGE: {metadata.language}\n"
        
        content += ":END:\n\n"
        
        # Add tags if present
        if org_tags:
            content += f"#+TAGS: {org_tags}\n\n"
        
        # Main heading with tags
        heading = f"* {title}"
        if org_tags:
            heading += f" {org_tags}"
        content += heading + "\n\n"

        # Add enhanced metadata section
        if metadata:
            content += "** Content Information\n\n"
            content += create_readable_summary(metadata) + "\n\n"

        # Add URL, local cache link, and wayback machine link
        content += "** Links\n\n"
        content += f"- Original URL: [[{url}][{url}]]\n"
        if local_filename:
            content += f"- Local Cache: [[file:local_cache/{local_filename}][Cached Copy]]\n"
        if wayback_url:
            content += f"- Internet Archive: [[{wayback_url}][Wayback Machine]]\n"
        content += f"- Status: {status}\n"
        content += f"- Date Added: {date_added}\n"
        if tags:
            content += f"- Original Tags: {tags}\n"

        # Add asset information
        if assets:
            asset_summary = format_asset_summary(assets)
            content += f"- Assets: {asset_summary}\n"

        content += "\n"

        # Add annotations if available
        if url in annotations:
            content += "** Highlights and Annotations\n\n"
            for highlight in annotations[url]:
                quote = highlight.get('quote', '')
                created_at = highlight.get('created_at', '')

                if quote:
                    content += f"#+BEGIN_QUOTE\n{quote}\n#+END_QUOTE\n"
                    if created_at:
                        highlight_date = self.timestamp_to_date(str(created_at))
                        content += f"/Highlighted on: {highlight_date}/\n"
                    content += "\n"

        # Add extracted article text if available
        if metadata and metadata.article_text and len(metadata.article_text.strip()) > 100:
            content += "** Article Summary\n\n"
            content += "#+BEGIN_QUOTE\n"
            # Truncate very long articles
            article_text = metadata.article_text.strip()
            if len(article_text) > 2000:
                article_text = article_text[:2000] + "..."
            content += article_text + "\n"
            content += "#+END_QUOTE\n\n"

        # Add space for notes
        content += "** Notes\n\n"
        content += "<!-- Add your notes here -->\n\n"

        return content

    def process_single_entry(self, row: Dict, annotations: Dict[str, List[Dict]],
                            output_dir: str, content_processor: EnhancedContentProcessor) -> Tuple[bool, str]:
        """Process a single entry with enhanced content processing."""
        try:
            title = row.get('title', 'Untitled').strip('"')
            url = row.get('url', '')
            time_added = row.get('time_added', '')

            # Create filename
            if time_added:
                dt = datetime.fromtimestamp(int(time_added))
                timestamp_prefix = dt.strftime('%Y%m%d_%H%M%S')
            else:
                timestamp_prefix = datetime.now().strftime('%Y%m%d_%H%M%S')

            sanitized_title = self.sanitize_filename(title, 80)
            filename = f"{timestamp_prefix}_{sanitized_title}.org"
            filepath = os.path.join(output_dir, filename)

            # Enhanced content processing
            local_filename = ""
            wayback_url = ""
            metadata = None
            assets = []

            if url:
                # Enhanced download with content processing
                success, local_filename, metadata, assets = content_processor.create_enhanced_cached_copy(url)

                # Check Wayback Machine
                wayback_available, wayback_url = self.check_wayback_machine_availability(url)

                # Update stats
                with self.stats_lock:
                    if success:
                        self.stats.download_success += 1
                        if metadata:
                            self.stats.metadata_extracted += 1
                            if metadata.article_text:
                                self.stats.readability_extracted += 1
                    else:
                        self.stats.download_fail += 1

                    if wayback_available:
                        self.stats.wayback_success += 1
                    else:
                        self.stats.wayback_fail += 1

                    # Update asset stats
                    successful_assets = [a for a in assets if a.success]
                    failed_assets = [a for a in assets if not a.success]
                    self.stats.assets_downloaded += len(successful_assets)
                    self.stats.assets_failed += len(failed_assets)
                    self.stats.total_content_size += sum(a.size for a in successful_assets)

            # Generate enhanced content
            content = self.create_enhanced_org_content(
                row, annotations, local_filename, wayback_url, metadata, assets
            )

            # Write file
            with open(filepath, 'w', encoding='utf-8') as org_file:
                org_file.write(content)

            with self.stats_lock:
                self.stats.processed_entries += 1

            return True, title

        except Exception as e:
            with self.stats_lock:
                self.stats.error_entries += 1
            return False, f"Error: {str(e)}"

    def create_enhanced_dashboard(self) -> Table:
        """Create an enhanced dashboard table with more detailed statistics."""
        table = Table(title="GetPocket to Org-Roam Converter - Enhanced Dashboard")

        table.add_column("Metric", style="cyan", no_wrap=True)
        table.add_column("Value", style="magenta")
        table.add_column("Rate/Info", style="green")

        # Calculate percentages and rates
        total = self.stats.total_entries
        processed = self.stats.processed_entries
        errors = self.stats.error_entries

        progress_pct = (processed / total * 100) if total > 0 else 0
        error_pct = (errors / total * 100) if total > 0 else 0

        download_success_pct = (self.stats.download_success / processed * 100) if processed > 0 else 0
        wayback_success_pct = (self.stats.wayback_success / processed * 100) if processed > 0 else 0
        metadata_pct = (self.stats.metadata_extracted / processed * 100) if processed > 0 else 0
        readability_pct = (self.stats.readability_extracted / processed * 100) if processed > 0 else 0

        # Format content size
        size_str = ""
        if self.stats.total_content_size > 0:
            if self.stats.total_content_size > 1024 * 1024:
                size_str = f"{self.stats.total_content_size / (1024 * 1024):.1f} MB"
            elif self.stats.total_content_size > 1024:
                size_str = f"{self.stats.total_content_size / 1024:.1f} KB"
            else:
                size_str = f"{self.stats.total_content_size} bytes"

        # Add rows
        table.add_row("Total Entries", str(total), "")
        table.add_row("Processed", f"{processed} ({progress_pct:.1f}%)", f"{self.stats.entries_per_second:.1f}/sec")
        table.add_row("Errors", f"{errors} ({error_pct:.1f}%)", "")
        table.add_row("", "", "")  # Separator

        table.add_row("Downloads Success", f"{self.stats.download_success} ({download_success_pct:.1f}%)", "")
        table.add_row("Downloads Failed", str(self.stats.download_fail), "")
        table.add_row("Metadata Extracted", f"{self.stats.metadata_extracted} ({metadata_pct:.1f}%)", "")
        table.add_row("Readability Extracted", f"{self.stats.readability_extracted} ({readability_pct:.1f}%)", "")
        table.add_row("", "", "")  # Separator

        table.add_row("Assets Downloaded", str(self.stats.assets_downloaded), "")
        table.add_row("Assets Failed", str(self.stats.assets_failed), "")
        table.add_row("Total Content Size", size_str, "")
        table.add_row("", "", "")  # Separator

        table.add_row("Wayback Found", f"{self.stats.wayback_success} ({wayback_success_pct:.1f}%)", "")
        table.add_row("Wayback Not Found", str(self.stats.wayback_fail), "")
        table.add_row("", "", "")  # Separator
        table.add_row("Elapsed Time", f"{self.stats.elapsed_time:.1f}s", "")

        return table

    def process_with_enhanced_interface(self, csv_file: str, annotations_file: str,
                                       output_dir: str, max_entries: Optional[int] = None):
        """Process entries with enhanced content processing and rich interface."""
        if not RICH_AVAILABLE:
            return self.process_without_rich(csv_file, annotations_file, output_dir, max_entries)

        # Setup directories
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        cache_dir = os.path.join(output_dir, "local_cache")
        Path(cache_dir).mkdir(parents=True, exist_ok=True)

        # Initialize enhanced content processor
        content_processor = EnhancedContentProcessor(cache_dir, max_workers=self.max_workers)

        # Load annotations
        annotations = self.load_annotations(annotations_file)

        # Read CSV entries
        entries = []
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if max_entries and i >= max_entries:
                    break
                entries.append(row)

        self.stats.total_entries = len(entries)

        # Create layout
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=3)
        )

        # Start processing with live display
        with Live(layout, refresh_per_second=4, screen=True) as live:
            # Update header
            layout["header"].update(Panel(
                Text("GetPocket to Org-Roam Converter - Enhanced Version", style="bold blue", justify="center"),
                style="blue"
            ))

            # Update footer
            layout["footer"].update(Panel(
                Text("Press Ctrl+C to stop | Enhanced with content processing & metadata extraction",
                     style="dim", justify="center"),
                style="dim"
            ))

            # Process entries in parallel
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tasks
                future_to_entry = {
                    executor.submit(self.process_single_entry, entry, annotations, output_dir, content_processor): entry
                    for entry in entries
                }

                # Process completed tasks
                for future in as_completed(future_to_entry):
                    try:
                        future.result()  # Just wait for completion
                        # Update dashboard
                        layout["main"].update(Panel(self.create_enhanced_dashboard(), style="green"))
                    except Exception:
                        with self.stats_lock:
                            self.stats.error_entries += 1

        # Get final cache statistics
        cache_stats = content_processor.get_cache_stats()

        # Final summary
        self.console.print("\n[bold green]Enhanced Processing Complete![/bold green]")
        self.console.print(f"Total entries: {self.stats.total_entries}")
        self.console.print(f"Processed: {self.stats.processed_entries}")
        self.console.print(f"Errors: {self.stats.error_entries}")
        self.console.print(f"Downloads successful: {self.stats.download_success}")
        self.console.print(f"Downloads failed: {self.stats.download_fail}")
        self.console.print(f"Metadata extracted: {self.stats.metadata_extracted}")
        self.console.print(f"Readability extracted: {self.stats.readability_extracted}")
        self.console.print(f"Assets downloaded: {self.stats.assets_downloaded}")
        self.console.print(f"Assets failed: {self.stats.assets_failed}")
        self.console.print(f"Total content size: {cache_stats['total_size'] / (1024*1024):.1f} MB")
        self.console.print(f"Wayback snapshots found: {self.stats.wayback_success}")
        self.console.print(f"Wayback snapshots not found: {self.stats.wayback_fail}")
        self.console.print(f"Output directory: {output_dir}")
        self.console.print(f"Cache directory: {cache_dir}")

    def process_without_rich(self, csv_file: str, annotations_file: str,
                           output_dir: str, max_entries: Optional[int] = None):
        """Fallback processing without rich interface."""
        print("GetPocket to Org-Roam Converter - Enhanced Version")
        print("=" * 60)

        # Setup directories
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        cache_dir = os.path.join(output_dir, "local_cache")
        Path(cache_dir).mkdir(parents=True, exist_ok=True)

        # Initialize enhanced content processor
        content_processor = EnhancedContentProcessor(cache_dir, max_workers=self.max_workers)

        # Load annotations
        annotations = self.load_annotations(annotations_file)
        print(f"Loaded annotations for {len(annotations)} URLs")

        # Read CSV entries
        entries = []
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if max_entries and i >= max_entries:
                    break
                entries.append(row)

        self.stats.total_entries = len(entries)
        print(f"Processing {len(entries)} entries with {self.max_workers} workers...")
        print("Enhanced features: content cleaning, metadata extraction, readability processing")

        # Process entries in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_entry = {
                executor.submit(self.process_single_entry, entry, annotations, output_dir, content_processor): entry
                for entry in entries
            }

            for future in as_completed(future_to_entry):
                try:
                    future.result()  # Just wait for completion
                    if self.stats.processed_entries % 25 == 0:
                        print(f"Processed {self.stats.processed_entries}/{self.stats.total_entries} entries... "
                              f"({self.stats.metadata_extracted} with metadata, "
                              f"{self.stats.assets_downloaded} assets downloaded)")
                except Exception:
                    pass

        # Get final cache statistics
        cache_stats = content_processor.get_cache_stats()

        # Final summary
        print(f"\nEnhanced Processing Complete!")
        print(f"Total entries: {self.stats.total_entries}")
        print(f"Processed: {self.stats.processed_entries}")
        print(f"Errors: {self.stats.error_entries}")
        print(f"Downloads successful: {self.stats.download_success}")
        print(f"Downloads failed: {self.stats.download_fail}")
        print(f"Metadata extracted: {self.stats.metadata_extracted}")
        print(f"Readability extracted: {self.stats.readability_extracted}")
        print(f"Assets downloaded: {self.stats.assets_downloaded}")
        print(f"Assets failed: {self.stats.assets_failed}")
        print(f"Total content size: {cache_stats['total_size'] / (1024*1024):.1f} MB")
        print(f"Wayback snapshots found: {self.stats.wayback_success}")
        print(f"Wayback snapshots not found: {self.stats.wayback_fail}")
        print(f"Output directory: {output_dir}")


def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description='Convert GetPocket export to org-roam files with enhanced content processing')
    parser.add_argument('--csv', default='part_000000.csv', help='CSV file path')
    parser.add_argument('--annotations', default='annotations/part_000000.json', help='Annotations JSON file path')
    parser.add_argument('--output', default='export_enhanced', help='Output directory')
    parser.add_argument('--workers', type=int, default=8, help='Number of parallel workers')
    parser.add_argument('--max-entries', type=int, help='Maximum number of entries to process (for testing)')

    args = parser.parse_args()

    if not os.path.exists(args.csv):
        print(f"Error: CSV file '{args.csv}' not found")
        return

    print("Enhanced GetPocket to Org-Roam Converter")
    print("Features: Content cleaning, metadata extraction, readability processing, enhanced asset handling")
    print("Dependencies: beautifulsoup4, lxml, readability-lxml, requests, chardet")
    print()

    converter = EnhancedPocketConverter(max_workers=args.workers)
    converter.process_with_enhanced_interface(args.csv, args.annotations, args.output, args.max_entries)


if __name__ == "__main__":
    main()
