#!/usr/bin/env python3
"""
Setup script for enhanced GetPocket to Org-Roam converter.
Checks dependencies and provides installation guidance.
"""

import sys
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 6):
        print("❌ Python 3.6 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    else:
        print(f"✅ Python version: {sys.version.split()[0]}")
        return True

def check_dependency(package_name, import_name=None, description=""):
    """Check if a dependency is installed."""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            print(f"✅ {package_name} - {description}")
            return True
        else:
            print(f"❌ {package_name} - {description}")
            return False
    except ImportError:
        print(f"❌ {package_name} - {description}")
        return False

def install_package(package_name):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main setup function."""
    print("GetPocket to Org-Roam Converter - Enhanced Setup")
    print("=" * 55)
    print()
    
    # Check Python version
    if not check_python_version():
        return
    
    print()
    print("Checking dependencies...")
    print()
    
    # Define dependencies
    dependencies = [
        ("rich", "rich", "Beautiful terminal interface"),
        ("beautifulsoup4", "bs4", "HTML parsing and cleaning"),
        ("lxml", "lxml", "Fast XML/HTML parser"),
        ("readability-lxml", "readability", "Article content extraction"),
        ("requests", "requests", "Enhanced HTTP client"),
        ("chardet", "chardet", "Character encoding detection"),
    ]
    
    missing_deps = []
    
    # Check each dependency
    for package_name, import_name, description in dependencies:
        if not check_dependency(package_name, import_name, description):
            missing_deps.append(package_name)
    
    print()
    
    if missing_deps:
        print(f"Missing dependencies: {len(missing_deps)}")
        print()
        print("To install missing dependencies, run:")
        print(f"pip install {' '.join(missing_deps)}")
        print()
        print("Or install all at once:")
        print("pip install -r requirements.txt")
        print()
        
        # Ask if user wants to install automatically
        try:
            response = input("Would you like to install missing dependencies now? (y/N): ")
            if response.lower() in ['y', 'yes']:
                print()
                print("Installing dependencies...")
                for package in missing_deps:
                    print(f"Installing {package}...")
                    if install_package(package):
                        print(f"✅ Successfully installed {package}")
                    else:
                        print(f"❌ Failed to install {package}")
                print()
                print("Installation complete!")
        except KeyboardInterrupt:
            print("\nInstallation cancelled.")
    else:
        print("✅ All dependencies are installed!")
    
    print()
    print("Available converters:")
    print()
    print("1. Basic converter (pocket_to_org_roam.py)")
    print("   - Simple conversion with basic caching")
    print("   - No additional dependencies required")
    print()
    print("2. Parallel converter (pocket_to_org_roam_parallel.py)")
    print("   - Multi-threaded processing")
    print("   - Rich terminal interface")
    print("   - Requires: rich")
    print()
    print("3. Enhanced converter (pocket_to_org_roam_enhanced.py)")
    print("   - Advanced content processing")
    print("   - Metadata extraction")
    print("   - Readability processing")
    print("   - Enhanced asset handling")
    print("   - Requires: all dependencies above")
    print()
    
    if not missing_deps:
        print("🎉 You can use any of the converters!")
        print()
        print("Quick start:")
        print("1. Extract your GetPocket export (pocket.zip)")
        print("2. Run: python3 pocket_to_org_roam_enhanced.py")
        print("3. Find your org-roam files in export_enhanced/")
        print()
        print("For testing:")
        print("python3 test_enhanced_converter.py")
    else:
        print("⚠️  Install missing dependencies to use the enhanced converter")
        print("You can still use the basic converter without additional dependencies")
    
    print()
    print("For more information, see:")
    print("- README.md - General usage and features")
    print("- ENHANCED_FEATURES.md - Enhanced converter details")

if __name__ == "__main__":
    main()
